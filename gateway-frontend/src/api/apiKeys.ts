import { apiRequest } from "@/utils/api";

// Types
export interface ApiCategory {
  id: string;
  name: string;
  category: string;
  provider: string;
  iconUrl: string | null;
  description: string | null;
  authType: string;
  authHeader: string;
  authPrefix: string | null;
  keyPrefix: string;
  keyPattern: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ApiKey {
  id: string;
  userId: string;
  categoryId: string;
  keyHash: string;
  keyPrefix: string;
  keyMask: string;
  name: string | null;
  status: 'active' | 'inactive' | 'revoked';
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  type: string;
  lastUsed: string | null;
  createdBy: string | null;
}

export interface ApiKeyDetail {
  id: string;
  name: string;
  description?: string | null;
  key: string;
  category: string;
  provider: string;
  status: 'active' | 'inactive' | 'revoked';
  createdAt: string;
  updatedAt?: string;
  lastUsed?: string | null;
  expirationDate?: string | null;
  stats: {
    requests: number;
    requestsChange: number;
    tokens: number;
    tokensChange: number;
    cost: number;
    lastCharge: string;
  };
}

export interface ApiKeyUsage {
  dailyRequests: { date: string; count: number }[];
  totalRequests: number;
  totalTokens: number;
  avgResponseTime: number;
  costThisMonth: number;
  usageByModel: {
    model: string;
    requests: number;
    tokens: number;
    cost: number;
  }[];
}

export interface ChannelUsage {
  provider: string;
  model: string;
  requests: number;
  tokens: number;
  cost: number;
  avgResponseTime: number;
}

export interface CreateApiKeyRequest {
  name: string;
  categoryId: string;
}

export interface UpdateApiKeyRequest {
  name?: string;
  status?: 'active' | 'inactive';
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  status?: 'active' | 'inactive' | 'revoked';
  categoryId?: string;
  search?: string;
  timeRange?: string;
}

// API Functions
/**
 * Create a new API key
 * @param data The API key data
 */
export const createApiKey = async (data: CreateApiKeyRequest): Promise<{ id: string; key: string; name: string; category: string; provider: string; createdAt: string }> => {
  const response = await apiRequest({
    url: 'api-keys',
    method: 'POST',
    body: data
  });
  return response.data;
};

/**
 * Update an API key
 * @param id The API key ID
 * @param data The update data
 */
export const updateApiKey = async (id: string, data: UpdateApiKeyRequest): Promise<void> => {
  await apiRequest({
    url: `api-keys/${id}`,
    method: 'PUT',
    body: data
  });
};

/**
 * Delete an API key
 * @param id The API key ID
 */
export const deleteApiKey = async (id: string): Promise<void> => {
  await apiRequest({
    url: `api-keys/${id}`,
    method: 'DELETE'
  });
};

/**
 * Get API keys
 * @param params Query parameters
 */
export const getApiKeys = async (params?: QueryParams): Promise<{ data: ApiKey[]; total: number; page: number; pageSize: number }> => {
  const response = await apiRequest({
    url: 'api-keys',
    method: 'GET',
    query: params
  });
  return response.data;
};

/**
 * Get API categories
 * @param onlyActive Only return active categories
 */
export const getApiCategories = async (onlyActive: boolean = true): Promise<ApiCategory[]> => {
  const response = await apiRequest({
    url: 'api-keys/categories',
    method: 'GET',
    query: { onlyActive }
  });
  return response.data.data;
};

/**
 * Get API key detail
 * @param id The API key ID
 */
export const getApiKeyDetail = async (id: string): Promise<ApiKeyDetail> => {
  try {
    const response = await apiRequest({
      url: `api-keys/${id}`,
      method: 'GET'
    });

    if (!response.success || !response.data || !response.data.data) {
      throw new Error('Invalid API key detail response');
    }

    return response.data.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get API key usage
 * @param id The API key ID
 * @param timeRange Time range in days
 */
export const getApiKeyUsage = async (id: string, timeRange?: string): Promise<ApiKeyUsage> => {
  try {
    const response = await apiRequest({
      url: `api-keys/${id}/usage`,
      method: 'GET',
      query: { timeRange }
    });

    if (!response.success || !response.data || !response.data.data) {
      throw new Error('Invalid API key usage response');
    }

    return response.data.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get usage summary
 * @param timeRange Time range in days
 */
export interface UsageSummary {
  totalRequests: number;
  totalTokens: number;
  avgResponseTime: number;
  costThisMonth: number;
}

export const getUsageSummary = async (timeRange?: string): Promise<UsageSummary> => {
  try {
    const response = await apiRequest({
      url: 'api-keys/reports/usage-summary',
      method: 'GET',
      query: { timeRange }
    });

    if (!response.success || !response.data || !response.data.data) {
      throw new Error('Invalid usage summary response');
    }

    return response.data.data;
  } catch (error) {
    // Return default data if API call fails
    return {
      totalRequests: 0,
      totalTokens: 0,
      avgResponseTime: 0,
      costThisMonth: 0
    };
  }
};

/**
 * Get channel usage
 * @param timeRange Time range in days
 */
export const getChannelUsage = async (timeRange?: string): Promise<ChannelUsage[]> => {
  try {
    const response = await apiRequest({
      url: 'api-keys/reports/channel-usage',
      method: 'GET',
      query: { timeRange }
    });

    if (!response.success || !response.data || !response.data.data) {
      throw new Error('Invalid channel usage response');
    }

    return response.data.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get overall usage data for reports
 * @param timeRange Time range in days
 */
export interface OverallUsageData {
  dailyData: {
    date: string;
    requests: number;
    tokens: number;
    cost: number;
  }[];
  monthlyData: {
    month: string;
    year: number;
    requests: number;
    tokens: number;
    cost: number;
  }[];
}

export const getOverallUsage = async (timeRange?: string): Promise<OverallUsageData> => {
  try {
    const response = await apiRequest({
      url: 'api-keys/reports/overall-usage',
      method: 'GET',
      query: { timeRange }
    });

    if (!response.success || !response.data || !response.data.data) {
      throw new Error('Invalid overall usage response');
    }

    return response.data.data;
  } catch (error) {
    // Return empty data if API call fails
    return {
      dailyData: [],
      monthlyData: []
    };
  }
};
