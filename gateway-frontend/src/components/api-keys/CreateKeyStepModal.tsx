'use client';

import { useState } from 'react';
import { Modal, Steps, Button, Form, Input, Select, Card, message } from 'antd';
import Image from 'next/image';
import { useLocaleContext } from '@/contexts/LocaleContext';
import styles from './CreateKeyStepModal.module.css';

const { Step } = Steps;
const { Option } = Select;

import { ApiCategory } from '@/api/apiKeys';
import Image1 from '../../../public/openai.png';
import Image2 from '../../../public/deepseek.png';
import Image3 from '../../../public/claude.png';
import Image4 from '../../../public/ollama.png';


interface CreateKeyStepModalProps {
  open: boolean;
  onClose: () => void;
  onComplete: (keyData: { name: string, channel: string }) => void;
  categories?: ApiCategory[];
}

// Default icons for known providers
const providerIcons: Record<string, string> = {
  'OpenAI': Image1.src,
  'DeepSeek': Image2.src,
  'Claude': Image3.src,
  'Ollama': Image4.src,
};

export default function CreateKeyStepModal({ open, onClose, onComplete, categories = [] }: CreateKeyStepModalProps) {
  const { messages } = useLocaleContext();
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);

  const handleNext = async () => {
    if (currentStep === 0) {
      try {
        // 验证第一步的表单
        await form.validateFields(['name']);
        const name = form.getFieldValue('name');

        if (!name || name.trim() === '') {
          message.error('Please enter a name for your API key');
          return;
        }

        console.log('Name validated:', name);
        setCurrentStep(1);
      } catch (error) {
        console.error('Form validation error:', error);
        message.error('Please enter a valid name for your API key');
      }
    } else if (currentStep === 1) {
      if (!selectedChannel) {
        message.error('Please select a channel');
        return;
      }
      setCurrentStep(2);
    }
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleFinish = () => {
    form.validateFields().then(values => {
      if (!selectedChannel) {
        message.error('Please select a channel');
        return;
      }

      // 确保name字段有值
      const name = values.name || form.getFieldValue('name');

      if (!name) {
        message.error('Please enter a name for your API key');
        setCurrentStep(0); // 返回到第一步
        return;
      }

      console.log('Form values:', values);
      console.log('Selected name:', name);
      console.log('Selected channel:', selectedChannel);

      // 调用onComplete函数，传递name和channel
      onComplete({
        name: name,
        channel: selectedChannel
      });

      // Reset form and state
      form.resetFields();
      setCurrentStep(0);
      setSelectedChannel(null);
    }).catch(errorInfo => {
      console.error('Validation failed:', errorInfo);
      message.error('Please check your input and try again');
    });
  };

  const handleCancel = () => {
    form.resetFields();
    setCurrentStep(0);
    setSelectedChannel(null);
    onClose();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={styles.stepContent}>
            <p className={styles.stepDescription}>
              {messages.apiKeys?.createModal?.nameDescription || "Enter a name for your API key to help you identify it later."}
            </p>
            <Form
              form={form}
              layout="vertical"
              initialValues={{ name: form.getFieldValue('name') || '' }}
              onValuesChange={(changedValues) => {
                console.log('Form values changed:', changedValues);
              }}
            >
              <Form.Item
                name="name"
                label={messages.apiKeys?.createModal?.nameLabel || "API Key Name"}
                rules={[{ required: true, message: messages.apiKeys?.createModal?.nameRequired || "Please enter a name" }]}
              >
                <Input
                  placeholder={messages.apiKeys?.createModal?.namePlaceholder || "e.g. Production API Key"}
                  className={styles.input}
                  autoFocus
                  onBlur={() => {
                    // 当输入框失去焦点时，保存当前值
                    console.log('Current name value:', form.getFieldValue('name'));
                  }}
                />
              </Form.Item>
            </Form>
          </div>
        );
      case 1:
        return (
          <div className={styles.stepContent}>
            <p className={styles.stepDescription}>
              {messages.apiKeys?.createModal?.description || "Select an API provider to create a key for."}
            </p>
            <div className={styles.channelList}>
              {categories.length === 0 ? (
                <div className={styles.noCategories}>
                  {"No API categories available"}
                </div>
              ) : (
                categories.map(category => {
                  // Get icon from category or use default based on provider
                  const iconUrl = category.iconUrl ||
                                providerIcons[category.provider] ||
                                providerIcons.Default;

                  return (
                    <Card
                      key={category.id}
                      className={`${styles.channelCard} ${selectedChannel === category.id ? styles.selectedCard : ''}`}
                      onClick={() => setSelectedChannel(category.id)}
                    >
                      <div className={styles.cardContent}>
                        <div className={styles.iconWrapper}>
                          <Image
                            src={iconUrl}
                            alt={category.provider}
                            width={32}
                            height={32}
                            className={styles.icon}
                          />
                        </div>
                        <div>
                          <h4 className={styles.title}>{category.provider}</h4>
                          <p className={styles.description}>
                            {category.description ||
                            messages.apiKeys.createModal.channelDesc[category.name as keyof typeof messages.apiKeys.createModal.channelDesc] ||
                            `${category.provider} ${category.category} API`}
                          </p>
                        </div>
                      </div>
                    </Card>
                  );
                })
              )}
            </div>
          </div>
        );
      case 2:
        return (
          <div className={styles.stepContent}>
            <div className={styles.summaryContainer}>
              <h3 className={styles.summaryTitle}>
                {messages.apiKeys?.createModal?.summaryTitle || "Review Your API Key Details"}
              </h3>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>
                  {messages.apiKeys?.createModal?.nameLabel || "API Key Name"}:
                </span>
                <span className={styles.summaryValue}>{form.getFieldValue('name')}</span>
              </div>
              <div className={styles.summaryItem}>
                <span className={styles.summaryLabel}>
                  {messages.apiKeys?.createModal?.channelLabel || "Channel"}:
                </span>
                <span className={styles.summaryValue}>
                  {categories.find(c => c.id === selectedChannel)?.provider || selectedChannel}
                </span>
              </div>
              <p className={styles.summaryNote}>
                {messages.apiKeys?.createModal?.summaryNote || "Click 'Create' to generate your API key. The key will be shown only once, so make sure to copy it."}
              </p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title={messages.apiKeys.createModal.title}
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      className={styles.modal}
    >
      <Steps current={currentStep} className={styles.steps}>
        <Step title={messages.apiKeys?.createModal?.step1 || "Name"} />
        <Step title={messages.apiKeys?.createModal?.step2 || "Channel"} />
        <Step title={messages.apiKeys?.createModal?.step3 || "Create"} />
      </Steps>

      {renderStepContent()}

      <div className={styles.footer}>
        {currentStep > 0 && (
          <Button onClick={handleBack} style={{ marginRight: 8 }}>
            {messages.apiKeys?.createModal?.back || "Back"}
          </Button>
        )}
        {currentStep < 2 ? (
          <Button type="primary" onClick={handleNext}>
            {messages.apiKeys?.createModal?.next || "Next"}
          </Button>
        ) : (
          <Button type="primary" onClick={handleFinish}>
            {messages.apiKeys?.createModal?.create || "Create"}
          </Button>
        )}
      </div>
    </Modal>
  );
}
