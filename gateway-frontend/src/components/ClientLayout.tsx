'use client';

import { Layout, Menu, ConfigProvider, MenuProps } from 'antd';

import { useState } from 'react';
import styles from './clientLayout.module.css';
import { useLocaleContext } from '@/contexts/LocaleContext';

import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import { useRouter, usePathname } from 'next/navigation';
import HeaderComponent from './Header';
import UserProviderToggle from './UserProviderToggle';
import { useUserProviderStore } from '@/store/userProviderStore';

const { Sider, Content } = Layout;




// export function SiderCollapsed() {
//   return (
//     <div className={styles.SiderCollapsed}>
//       <span>SIGHT</span>
//       <img src={Collapsed.src} alt="" />
//     </div>
//   );
// }

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [collapsed, setCollapsed] = useState(false);
  const { locale, messages } = useLocaleContext();
  const router = useRouter();
  const pathname = usePathname();
  const { mode: userProviderMode } = useUserProviderStore();

  // 首页隐藏侧边栏和头部
  if (pathname === '/') {
    return <>{children}</>;
  }

  const handleMenuClick = (key: string) => {
    switch (key) {
      case 'api-keys':
        router.push('/api-keys');
        break;
      case 'api-keys-reports':
        router.push('/api-keys/reports');
        break;
      case 'models':
        router.push('/models');
        break;
      case 'earnings':
        router.push('/earnings');
        break;
      case 'docs':
        router.push('/docs');
        break;
      case 'nodes':
        router.push('/nodes');
        break;
      case 'management-dashboard':
        router.push('/management/dashboard');
        break;
      case 'management-history':
        router.push('/management/history');
        break;
      case 'management-nodes':
        router.push('/management/nodes');
        break;
    }
  };

  // Define menu items by role/section
  const apiKeyMenuItems = {
    type: 'group' as const,
    label: messages.menu.keys.apiGroup,
    key: 'api-group',
    children: [
      {
        key: 'api-keys',
        // icon: <KeyOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}></span>,
      },
      {
        key: 'api-keys-reports',
        // icon: <BarChartOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>{messages.menu.keys.reports}</span>,
      },
      {
        key: 'models',
        // icon: <AppstoreOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>{messages.menu.keys.models}</span>,
      }
    ],
  };

  const nodeMenuItems = {
    type: 'group' as const,
    label: messages.menu.earnings.title,
    key: 'nodes-group',
    children: [
      {
        key: 'nodes',
        // icon: <DashboardOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>{messages.menu.nodes.title}</span>,
      },
      {
        key: 'earnings',
        // icon: <DollarOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>{messages.menu.earnings.title}</span>,
      }
    ],
  };

  const managementMenuItems = {
    type: 'group' as const,
    label: 'Management',
    key: 'management-group',
    children: [
      {
        key: 'management-dashboard',
        // icon: <DashboardOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>Dashboard</span>,
      },
      {
        key: 'management-history',
        // icon: <FileTextOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>History</span>,
      },
      {
        key: 'management-nodes',
        // icon: <RobotOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>Node Management</span>,
      }
    ],
  };

  const docsMenuItems = {
    type: 'group' as const,
    label: messages.menu.docs.sysGroup,
    key: 'sys-group',
    children: [
      {
        key: 'docs',
        // icon: <SettingOutlined className={styles.menuIcon} />,
        label: <span className={styles.menuLabel}>{messages.menu.docs.title}</span>,
      },
    ],
  };

  // Define which menu items to show based on the current path
  const keysPaths = ['/api-keys', '/api-keys/reports', '/models'];
  const nodesPaths = ['/nodes', '/earnings'];
  const managementPaths = ['/management'];
  const docsPaths = ['/docs'];

  // Get the current section based on the path
  const getCurrentSection = () => {
    const path = pathname || '';

    if (keysPaths.some(p => path.startsWith(p))) {
      return 'keys';
    } else if (nodesPaths.some(p => path.startsWith(p))) {
      return 'nodes';
    } else if (managementPaths.some(p => path.startsWith(p))) {
      return 'management';
    } else if (docsPaths.some(p => path.startsWith(p))) {
      return 'docs';
    }

    // Default to showing all menu items if path doesn't match any section
    return 'all';
  };

  // Filter menu items based on the current section
  const getMenuItemsForCurrentSection = () => {
    const currentSection = getCurrentSection();

    switch (currentSection) {
      case 'keys':
        return [apiKeyMenuItems];
      case 'nodes':
        return [nodeMenuItems];
      case 'management':
        return [managementMenuItems];
      case 'docs':
        return [docsMenuItems];
      default:
        // Show all menu items if no specific section is matched
        return [apiKeyMenuItems];
    }
  };

  const menuItems: MenuProps['items'] = getMenuItemsForCurrentSection();

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = pathname?.split('/')[1] || '';
    const subPath = pathname?.split('/')[2] || '';

    // 处理特殊路径
    if (path === 'api-keys' && subPath === 'reports') {
      return ['api-keys-reports'];
    }

    // 处理 management 路径
    if (path === 'management') {
      return [`management-${subPath}`];
    }

    return [path];
  };

  // 获取当前页面标题
  const getPageTitle = () => {
    const path = pathname?.split('/')[1] || 'api-keys';
    const subPath = pathname?.split('/')[2] || '';

    switch (path) {
      case 'api-keys':
        if (subPath === 'reports') {
          return messages.reports?.title || "API Keys Usage Report";
        }
        return messages.apiKeys.title;
      case 'models':
        return messages.models?.description || "AI Models";
      case 'nodes':
        return messages.menu.nodes.title;
      case 'management':
        switch (subPath) {
          case 'dashboard':
            return 'Dashboard';
          case 'history':
            return 'History';
          case 'nodes':
            return 'Node Management';
          default:
            return 'Management';
        }
      case 'docs':
        return messages.menu.docs.title;
      case 'earnings':
        return messages.menu.earnings.title;
    }
  };

  return (
    <ConfigProvider locale={locale === 'zh' ? zhCN : enUS}>
      <Layout className={styles.root}>
        {/* Header at the top */}
        <HeaderComponent
          collapsed={collapsed}
          setCollapsed={setCollapsed}
          pageTitle={getPageTitle()}
        />
        <Layout className={styles.mainLayout}>
          {/* Sidebar below the header */}
          {/* <SiderCollapsed collapsed={collapsed} setCollapsed={setCollapsed} /> */}
         {(
          pathname.includes('api-keys') ||
          pathname.includes('models') ||
          pathname.includes('management')
         ) && <Sider
            className={styles.sider}
            width={250}
            collapsedWidth={80}
            trigger={null}
            collapsible
            collapsed={collapsed}
          >
            {!collapsed && (
              <div className={styles.siderHeader}>
                <UserProviderToggle />
              </div>
            )}
            <Menu
              mode="inline"
              selectedKeys={getSelectedKey()}
              items={menuItems}
              className={styles.menu}
              inlineCollapsed={collapsed}
              onClick={({ key }) => handleMenuClick(key)}
            />
          </Sider>}
          <Content className={styles.content}>{children}</Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
}