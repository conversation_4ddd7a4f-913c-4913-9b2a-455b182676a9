'use client';

import { useState, useEffect } from 'react';
import { Button, Typography, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useLocaleContext } from '@/contexts/LocaleContext';
import ChannelCards from '@/components/api-keys/ChannelCards';
import ApiKeyTable from '@/components/api-keys/ApiKeyTable';
import CreateKeyStepModal from '@/components/api-keys/CreateKeyStepModal';
import KeyGeneratedModal from '@/components/api-keys/KeyGeneratedModal';
import { createApiKey, getApiKeys, getApiCategories, ApiKey, ApiCategory } from '@/api/apiKeys';
import styles from './page.module.css';

const { Title, Text } = Typography;

const EXAMPLE_CURL = `curl https://api.deepseek.com/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer <DeepSeek API Key>" \\
  -d '{
      "model": "deepseek-chat",
      "messages": [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello!"}
      ],
      "stream": false
  }'`;

export default function ApiKeysPage() {
  const { messages } = useLocaleContext();
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [generatedModalOpen, setGeneratedModalOpen] = useState(false);
  const [generatedKey, setGeneratedKey] = useState('');
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [categories, setCategories] = useState<ApiCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalKeys, setTotalKeys] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Fetch API keys and categories
  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch API keys
      const keysResponse = await getApiKeys({
        page: currentPage,
        pageSize: pageSize
      });
      setApiKeys(keysResponse.data);
      setTotalKeys(keysResponse.total);

      // Fetch API categories
      const categoriesData = await getApiCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize]);

  const handleCreateKey = async (keyData: { name: string, channel: string }) => {
    try {
      console.log('Creating key with data:', keyData);

      if (!keyData.name || !keyData.channel) {
        console.error('Missing required data:', keyData);
        message.error('Missing required data. Please provide both name and channel.');
        return;
      }

      // Call the API to create a new key
      const response = await createApiKey({
        name: keyData.name,
        categoryId: keyData.channel
      });

      console.log('API response:', response);

      setGeneratedKey(response.key);
      setCreateModalOpen(false);
      setGeneratedModalOpen(true);
      message.success(messages.apiKeys.generatedModal.created, 1);

      // Refresh the API keys list
      fetchData();
    } catch (error) {
      console.error('Error creating API key:', error);
      message.error(messages.apiKeys.generatedModal.createFailed || 'Failed to create API key');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <Text type="secondary">
            {messages.apiKeys.description.split('<br>').map((line: string, i: number) => (
              <span key={i}>
                {line}
                <br />
              </span>
            ))}
          </Text>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className={styles.createButton}
          onClick={() => setCreateModalOpen(true)}
        >
          {messages.apiKeys.createButton}
        </Button>
      </div>

      <div className={styles.section}>
        <Title level={5}>{messages.apiKeys.availableChannel}</Title>
        <ChannelCards categories={categories} />
      </div>

      <div className={styles.section}>
        <ApiKeyTable
          loading={loading}
          dataSource={apiKeys}
          total={totalKeys}
          page={currentPage}
          pageSize={pageSize}
          onPageChange={(page, size) => {
            setCurrentPage(page);
            if (size) setPageSize(size);
          }}
          onRefresh={fetchData}
        />
      </div>

      <CreateKeyStepModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onComplete={handleCreateKey}
        categories={categories}
      />

      <KeyGeneratedModal
        open={generatedModalOpen}
        onClose={() => setGeneratedModalOpen(false)}
        apiKey={generatedKey}
        example={EXAMPLE_CURL}
      />
    </div>
  );
}